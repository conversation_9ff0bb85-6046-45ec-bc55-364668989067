import { WorldMap, WorldMapCell } from '../../../shared/types/World';
import { TerrainType, WorldMapDecorations } from '../../../shared/enums';
import {  TILE_GAP, DECORATION_TEXTURE_SETTINGS, WORLD_MAP_TERRAIN_TEXTURE_SETTINGS } from '../../utils/constants/rendering';
import { TERRAIN_TEXTURES } from '../textures/terrain';
import { DECORATION_TEXTURES} from '../textures/decorations';
import { getTileCenterOnScreen, isTileVisible } from '../../utils/coordinates/isometric';
import { drawPlayerSkeleton } from '../animations/animationSystem';
import { drawPatternTexture, supportsPatternTextures } from './drawPatterns/drawPatternTexruteWM';
import { textureLoader } from '../textures/TextureLoader';


/**
 * Получает цвет окрашивания для клетки на основе декораций мировой карты
 */


/**
 * Получает цвет окрашивания для клетки на основе локации
 */
export const getLocationColor = (tileData: WorldMapCell | undefined): string | null => {
  if (!tileData || !tileData.location) {
    return null; // Нет локации - не окрашиваем
  }

  // Желтый цвет для всех локаций
  return '#FFD700'; // Золотисто-желтый
};

/**
 * Отрисовывает цветовую подсветку декорации на клетке
 */
export const drawDecorationOverlay = (
  ctx: CanvasRenderingContext2D,
  centerX: number,
  centerY: number,
  halfTileW: number,
  halfTileH: number,
  decorationColor: string,
  opacity: number = 0.4
): void => {
  // Сохраняем состояние контекста
  ctx.save();

  // Создаем ромбовидную область отсечения
  ctx.beginPath();
  ctx.moveTo(centerX, centerY - halfTileH); // Верх
  ctx.lineTo(centerX + halfTileW, centerY); // Право
  ctx.lineTo(centerX, centerY + halfTileH); // Низ
  ctx.lineTo(centerX - halfTileW, centerY); // Лево
  ctx.closePath();
  ctx.clip();

  // Устанавливаем прозрачность и цвет
  ctx.globalAlpha = opacity;
  ctx.fillStyle = decorationColor;
  
  // Заливаем область
  ctx.fill();

  // Восстанавливаем состояние контекста
  ctx.restore();
};

/**
 * Отрисовывает цветовую подсветку локации на клетке
 */
export const drawLocationOverlay = (
  ctx: CanvasRenderingContext2D,
  centerX: number,
  centerY: number,
  halfTileW: number,
  halfTileH: number,
  locationColor: string,
  opacity: number = 0.8
): void => {
  // Сохраняем состояние контекста
  ctx.save();

  // Создаем ромбовидную область отсечения
  ctx.beginPath();
  ctx.moveTo(centerX, centerY - halfTileH); // Верх
  ctx.lineTo(centerX + halfTileW, centerY); // Право
  ctx.lineTo(centerX, centerY + halfTileH); // Низ
  ctx.lineTo(centerX - halfTileW, centerY); // Лево
  ctx.closePath();
  ctx.clip();

  // Устанавливаем прозрачность и цвет
  ctx.globalAlpha = opacity;
  ctx.fillStyle = locationColor;
  
  // Заливаем область
  ctx.fill();

  // Восстанавливаем состояние контекста
  ctx.restore();
};

/**
 * Менеджер текстур местности - теперь использует централизованный кэш
 */
class TerrainTextureManager {
  /**
   * Получает текстуры для указанного типа местности через centralizedLoader
   */
  async getTerrainTextures(terrainType: TerrainType): Promise<HTMLImageElement[]> {
    const terrainKey = terrainType.toLowerCase() as keyof typeof TERRAIN_TEXTURES;

    // Получаем пути к текстурам
    const texturePaths = TERRAIN_TEXTURES[terrainKey];
    if (!texturePaths) {
      return [];
    }

    // Загружаем все текстуры через централизованный loader
    try {
      const images = await Promise.all(texturePaths.map(path => textureLoader.loadTexture(path)));
      return images;
    } catch (error) {
      return [];
    }
  }

  /**
   * Синхронно получает текстуры (если они уже загружены)
   */
  getLoadedTextures(terrainType: TerrainType): HTMLImageElement[] | null {
    const terrainKey = terrainType.toLowerCase() as keyof typeof TERRAIN_TEXTURES;
    const texturePaths = TERRAIN_TEXTURES[terrainKey];
    if (!texturePaths) {
      return null;
    }

    // Проверяем загруженные текстуры через centralizedный кэш
    const loadedTextures = texturePaths
      .map(path => textureLoader.getTexture(path))
      .filter(img => img !== undefined) as HTMLImageElement[];
    
    return loadedTextures.length > 0 ? loadedTextures : null;
  }

  /**
   * Предзагружает все текстуры местности
   */
  async preloadAllTextures(): Promise<void> {
    const loadPromises = Object.keys(TERRAIN_TEXTURES).map((terrainKey) => this.getTerrainTextures(terrainKey.toUpperCase() as TerrainType));

    await Promise.allSettled(loadPromises);
  }
}

// Глобальный экземпляр менеджера текстур
export const terrainTextureManager = new TerrainTextureManager();

/**
 * Менеджер текстур декораций - теперь использует централизованный кэш
 */
class DecorationTextureManager {
  /**
   * Получает текстуры для указанного типа декорации через centralizedLoader
   */
  async getDecorationTextures(decorationType: WorldMapDecorations): Promise<HTMLImageElement[]> {
    const decorationKey = decorationType.toLowerCase() as keyof typeof DECORATION_TEXTURES;

    // Получаем пути к текстурам
    const texturePaths = DECORATION_TEXTURES[decorationKey];
    if (!texturePaths) {
      return [];
    }

    // Загружаем все текстуры через централизованный loader
    try {
      const images = await Promise.all(texturePaths.map(path => textureLoader.loadTexture(path)));
      return images;
    } catch (error) {
      return [];
    }
  }

  /**
   * Синхронно получает текстуры (если они уже загружены)
   */
  getLoadedTextures(decorationType: WorldMapDecorations): HTMLImageElement[] | null {
    const decorationKey = decorationType.toLowerCase() as keyof typeof DECORATION_TEXTURES;
    const texturePaths = DECORATION_TEXTURES[decorationKey];
    if (!texturePaths) {
      return null;
    }

    // Проверяем загруженные текстуры через централизованный кэш
    const loadedTextures = texturePaths
      .map(path => textureLoader.getTexture(path))
      .filter(img => img !== undefined) as HTMLImageElement[];
    
    return loadedTextures.length > 0 ? loadedTextures : null;
  }

  /**
   * Предзагружает все текстуры декорац��й
   */
  async preloadAllTextures(): Promise<void> {
    const loadPromises = Object.keys(DECORATION_TEXTURES).map((decorationKey) =>
      this.getDecorationTextures(decorationKey.toUpperCase() as WorldMapDecorations)
    );

    await Promise.allSettled(loadPromises);
  }
}

// Глобальный экземпляр менеджера текстур декораций
export const decorationTextureManager = new DecorationTextureManager();

/**
 * Отрисовывает ТОЛЬКО декорации для тайла (без фона и местности)
 */
export const drawTileDecorations = (
  ctx: CanvasRenderingContext2D,
  screenX: number,
  screenY: number,
  isoX: number,
  isoY: number,
  tileWidth: number,
  tileHeight: number,
  canvasWidth: number,
  canvasHeight: number,
  cameraX: number,
  cameraY: number,
  currentWorld: WorldMap | null,
  cellTarget: { isoX: number; isoY: number; tileData: any } | null
) => {
  const { centerX, centerY } = getTileCenterOnScreen(screenX, screenY, canvasWidth, canvasHeight, cameraX, cameraY);

  // Проверяем, находится ли тайл в видимой об��асти
  if (!isTileVisible(centerX, centerY, tileWidth, tileHeight, canvasWidth, canvasHeight)) {
    return;
  }

  const halfTileW = tileWidth / 2 - TILE_GAP;
  const halfTileH = tileHeight / 2 - TILE_GAP;

  // Получаем данные тайла из текущего мира
  const tileKey = `${isoX},${isoY}`;
  const tileData = currentWorld?.worldMap?.[tileKey];

  // Отрисовываем ТОЛЬКО декорации
  if (tileData?.decoration && tileData.decoration !== WorldMapDecorations.NONE) {
    // Проверяем, поддерживает ли декорация паттерновые текстуры
    if (supportsPatternTextures(tileData)) {
      // Используем паттерновые текстуры для озер
      drawPatternTexture(ctx, centerX, centerY, halfTileW, halfTileH, tileData);
    } else {
      // Для озер, рек и дорог с пустым decorationBorder НЕ рендерим ничего
      const isPatternDecoration = tileData.decoration === WorldMapDecorations.LAKE || 
                                  tileData.decoration === WorldMapDecorations.RIVER ||
                                  tileData.decoration === WorldMapDecorations.ROAD;
      
      if (!isPatternDecoration) {
        // Используем обычные текстуры декораций только для НЕ-паттерновых декораций
        const textures = decorationTextureManager.getLoadedTextures(tileData.decoration);
        if (textures && textures.length > 0) {
          drawDecorationTexture(
            ctx,
            centerX,
            centerY,
            halfTileW,
            halfTileH,
            tileData.decoration,
            tileData.imgDirection ? tileData.imgDirection - 1 : undefined,
            0 // без поворота
          );
        }
      }
      // Если это паттерновая декорация (озеро/река/дорога) с пустым decorationBorder - не рендерим ничего
    }
  }
};

/**
 * Отрисовывает текстуру декорации на ромбовидный тайл
 */
export const drawDecorationTexture = (
  ctx: CanvasRenderingContext2D,
  centerX: number,
  centerY: number,
  halfTileW: number,
  halfTileH: number,
  decorationType: WorldMapDecorations,
  textureVariation?: number,
  rotation: number = 0
): void => {
  // Получаем загруженные текстуры
  const textures = decorationTextureManager.getLoadedTextures(decorationType);
  if (!textures || textures.length === 0) {
    return; // Текстуры еще не загружены или не найдены
  }

  // Выбираем вариацию текстуры
  const variation = textureVariation !== undefined && textureVariation < textures.length
    ? textureVariation
    : Math.floor(Math.random() * textures.length);

  const texture = textures[variation];
  if (!texture || !texture.complete) {
    return; // Текстура не загружена
  }

  // Сохраняем состояние контекста
  ctx.save();

  
  // Применяем поворот если нужно
  if (rotation !== 0) {
    ctx.translate(centerX, centerY);
    ctx.rotate(rotation);
    ctx.translate(-centerX, -centerY);
  }

  // Вычисляем размеры с учетом настроек
  let drawW = texture.width;
  let drawH = texture.height;

  if (DECORATION_TEXTURE_SETTINGS.ENABLE_SCALING) {
    // Получаем множитель масштаба для данного типа декорации
    const scaleKey = decorationType.toUpperCase() as keyof typeof DECORATION_TEXTURE_SETTINGS.SCALE_MULTIPLIERS;
    const scaleMultiplier = DECORATION_TEXTURE_SETTINGS.SCALE_MULTIPLIERS[scaleKey] || 1.0;
    
    // Применяем базовые размеры и множитель
    drawW = DECORATION_TEXTURE_SETTINGS.DEFAULT_WIDTH * scaleMultiplier;
    drawH = DECORATION_TEXTURE_SETTINGS.DEFAULT_HEIGHT * scaleMultiplier;
    
    // Сохраняем пропорции если нужно
    if (DECORATION_TEXTURE_SETTINGS.PRESERVE_ASPECT) {
      const aspectRatio = texture.width / texture.height;
      if (aspectRatio > 1) {
        // Широкое изображение - подгоняем высоту
        drawH = drawW / aspectRatio;
      } else {
        // Высокое изображение - подгоняем ширину
        drawW = drawH * aspectRatio;
      }
    }
  }

  // Вычисляем смещение позиции если включено
  let offsetX = 0;
  let offsetY = 0;
  
  if (DECORATION_TEXTURE_SETTINGS.ENABLE_OFFSET) {
    const scaleKey = decorationType.toUpperCase() as keyof typeof DECORATION_TEXTURE_SETTINGS.VERTICAL_OFFSET;
    offsetX = DECORATION_TEXTURE_SETTINGS.HORIZONTAL_OFFSET[scaleKey] || 0;
    offsetY = DECORATION_TEXTURE_SETTINGS.VERTICAL_OFFSET[scaleKey] || 0;
  }

  // Отрисовываем текстуру с вычисленными размерами и смещением
  ctx.drawImage(texture, centerX - drawW / 2 + offsetX, centerY - drawH / 2 + offsetY, drawW, drawH);

  // Восстанавливаем состояние контекста
  ctx.restore();
};

/**
 * Отрисовывает текстуру местности на ромбовидный тайл
 */
export const drawTerrainTexture = (
  ctx: CanvasRenderingContext2D,
  centerX: number,
  centerY: number,
  halfTileW: number,
  halfTileH: number,
  terrainType: TerrainType,
  textureVariation?: number,
  rotation: number = 0
): void => {
  // Получаем загруженные текстуры
  const textures = terrainTextureManager.getLoadedTextures(terrainType);
  if (!textures || textures.length === 0) {
    return; // Текстуры еще не загружены или не найдены
  }

  // Выбираем вариацию текстуры
  const variation =
    textureVariation !== undefined ? Math.max(0, Math.min(textureVariation, textures.length - 1)) : Math.floor(Math.random() * textures.length);

  const texture = textures[variation];
  if (!texture || !texture.complete) {
    return; // Текстура не загружена
  }

  // Сохраняем состояние контекста
  ctx.save();

  // Создаем ромбовидную область отсечения (если включено)
  if (WORLD_MAP_TERRAIN_TEXTURE_SETTINGS.CLIP_TO_DIAMOND) {
    ctx.beginPath();
    ctx.moveTo(centerX, centerY - halfTileH); // Верх
    ctx.lineTo(centerX + halfTileW, centerY); // Право
    ctx.lineTo(centerX, centerY + halfTileH); // Низ
    ctx.lineTo(centerX - halfTileW, centerY); // Лево
    ctx.closePath();
    ctx.clip();
  }

  // Получаем настройки для данного типа местности
  const terrainKey = terrainType.toUpperCase() as keyof typeof WORLD_MAP_TERRAIN_TEXTURE_SETTINGS.WIDTH_SCALE;
  
  // Вычисляем угол поворота
  let finalRotation = rotation;
  if (WORLD_MAP_TERRAIN_TEXTURE_SETTINGS.ENABLE_ROTATION) {
    const terrainRotation = WORLD_MAP_TERRAIN_TEXTURE_SETTINGS.ROTATION_ANGLE[terrainKey] || 0;
    finalRotation += terrainRotation;
  }

  // Применяем поворот если нужно
  if (finalRotation !== 0) {
    ctx.translate(centerX, centerY);
    ctx.rotate(finalRotation);
    ctx.translate(-centerX, -centerY);
  }

  // Вычисляем размеры с учетом настроек
  let drawW: number;
  let drawH: number;

  if (WORLD_MAP_TERRAIN_TEXTURE_SETTINGS.ENABLE_SCALING) {
    // Используем настройки масштабирования
    const widthScale = WORLD_MAP_TERRAIN_TEXTURE_SETTINGS.WIDTH_SCALE[terrainKey] || 1.0;
    const heightScale = WORLD_MAP_TERRAIN_TEXTURE_SETTINGS.HEIGHT_SCALE[terrainKey] || 1.0;
    
    if (WORLD_MAP_TERRAIN_TEXTURE_SETTINGS.SEPARATE_PROPORTIONS) {
      // Отдельные пропорции для ширины и высоты
      drawW = WORLD_MAP_TERRAIN_TEXTURE_SETTINGS.DEFAULT_WIDTH * widthScale;
      drawH = WORLD_MAP_TERRAIN_TEXTURE_SETTINGS.DEFAULT_HEIGHT * heightScale;
    } else {
      // Пропорциональное масштабирование
      const avgScale = (widthScale + heightScale) / 2;
      drawW = WORLD_MAP_TERRAIN_TEXTURE_SETTINGS.DEFAULT_WIDTH * avgScale;
      drawH = WORLD_MAP_TERRAIN_TEXTURE_SETTINGS.DEFAULT_HEIGHT * avgScale;
      
      // Сохраняем пропорции оригинальной текстуры
      const aspectRatio = texture.width / texture.height;
      if (aspectRatio > 1) {
        drawH = drawW / aspectRatio;
      } else {
        drawW = drawH * aspectRatio;
      }
    }
  } else {
    // Старый способ - покрытие ромба
    const tileWidth = halfTileW * 2;
    const tileHeight = halfTileH * 2;
    const imgRatio = texture.width / texture.height;
    const tileRatio = tileWidth / tileHeight;

    drawW = tileWidth;
    drawH = tileHeight;

    if (imgRatio > tileRatio) {
      // Изображение шире ромба - увеличиваем высоту
      drawH = tileWidth / imgRatio;
      if (drawH < tileHeight) drawH = tileHeight;
      drawW = drawH * imgRatio;
    } else {
      // Изображение выше ромба - увеличиваем ширину
      drawW = tileHeight * imgRatio;
      if (drawW < tileWidth) drawW = tileWidth;
      drawH = drawW / imgRatio;
    }
  }

  // Вычисляем смещение позиции если включено
  let offsetX = 0;
  let offsetY = 0;
  
  if (WORLD_MAP_TERRAIN_TEXTURE_SETTINGS.ENABLE_OFFSET) {
    offsetX = WORLD_MAP_TERRAIN_TEXTURE_SETTINGS.HORIZONTAL_OFFSET[terrainKey] || 0;
    offsetY = WORLD_MAP_TERRAIN_TEXTURE_SETTINGS.VERTICAL_OFFSET[terrainKey] || 0;
  }

  // Отрисовываем текстуру с вычисленными размерами и смещением
  ctx.drawImage(texture, centerX - drawW / 2 + offsetX, centerY - drawH / 2 + offsetY, drawW, drawH);

  // Восстанавливаем состояние контекста
  ctx.restore();
};

/**
 * Отрисовывает ромбовидный тайл
 */
export const drawTile = (
  ctx: CanvasRenderingContext2D,
  screenX: number,
  screenY: number,
  isoX: number,
  isoY: number,
  tileWidth: number,
  tileHeight: number,
  canvasWidth: number,
  canvasHeight: number,
  cameraX: number,
  cameraY: number,
  currentWorld: WorldMap | null,
  cellTarget: { isoX: number; isoY: number; tileData: any } | null,
  drawPlayer: boolean = true,
  drawDecorations: boolean = true
) => {
  const { centerX, centerY } = getTileCenterOnScreen(screenX, screenY, canvasWidth, canvasHeight, cameraX, cameraY);

  // Проверяем, находится ли тайл в видимой области
  if (!isTileVisible(centerX, centerY, tileWidth, tileHeight, canvasWidth, canvasHeight)) {
    return;
  }

  const halfTileW = tileWidth / 2 - TILE_GAP;
  const halfTileH = tileHeight / 2 - TILE_GAP;

  // Получаем данные тайла из текущего мира
  const tileKey = `${isoX},${isoY}`;
  const tileData = currentWorld?.worldMap?.[tileKey];

  // Рисуем ромб с отступом
  ctx.beginPath();
  ctx.moveTo(centerX, centerY - halfTileH); // Верх
  ctx.lineTo(centerX + halfTileW, centerY); // Право
  ctx.lineTo(centerX, centerY + halfTileH); // Низ
  ctx.lineTo(centerX - halfTileW, centerY); // Лево
  ctx.closePath();

  // Сначала отрисовываем текстуру местности
  if (tileData?.terrain) {
    drawTerrainTexture(
      ctx,
      centerX,
      centerY,
      halfTileW,
      halfTileH,
      tileData.terrain,
      tileData.imgDirection ? tileData.imgDirection - 1 : undefined, // Используем imgDirection как вариацию текстуры
      0 // Поворот теперь настраивается в TERRAIN_TEXTURE_SETTINGS
    );
  } 

  // Затем отрисовываем де��орации ПОВЕРХ текстуры местности (только если разрешено)
  if (drawDecorations && tileData?.decoration && tileData.decoration !== WorldMapDecorations.NONE) {
    // Проверяем, поддерживает ли декорация паттерновые текстуры
    if (supportsPatternTextures(tileData)) {
      // Используем паттерновые текстуры для озер
      drawPatternTexture(ctx, centerX, centerY, halfTileW, halfTileH, tileData);
    } else {
      // Для озер, рек и дорог с пустым decorationBorder НЕ рендерим ничего
      const isPatternDecoration = tileData.decoration === WorldMapDecorations.LAKE || 
                                  tileData.decoration === WorldMapDecorations.RIVER ||
                                  tileData.decoration === WorldMapDecorations.ROAD;
      
      if (!isPatternDecoration) {
        // Используем обычные текстуры декораций только для НЕ-паттерновых декораций
        const textures = decorationTextureManager.getLoadedTextures(tileData.decoration);
        if (textures && textures.length > 0) {
          drawDecorationTexture(
            ctx,
            centerX,
            centerY,
            halfTileW,
            halfTileH,
            tileData.decoration,
            tileData.imgDirection ? tileData.imgDirection - 1 : undefined,
            0 // без поворота
          );
        } 
      }
      // Если это паттерновая декорация (озеро/река/дорога) с пустым decorationBorder - не рендерим ничего
    }
  }

  // Подсветка выбранного тайла внутренним лаймовым кантиком
  if (cellTarget && cellTarget.isoX === isoX && cellTarget.isoY === isoY) {
    // Сохраняем состояние контекста
    ctx.save();

    // Создаем внутренний ромб меньшего размера для кантика
    const insetSize = 4; // Размер отступа для кантика
    const innerHalfW = halfTileW - insetSize;
    const innerHalfH = halfTileH - insetSize;

    // Создаем область отсечения для внутреннего ромба
    ctx.beginPath();
    ctx.moveTo(centerX, centerY - innerHalfH);
    ctx.lineTo(centerX + innerHalfW, centerY);
    ctx.lineTo(centerX, centerY + innerHalfH);
    ctx.lineTo(centerX - innerHalfW, centerY);
    ctx.closePath();
    ctx.clip();

    // Рисуем лаймовую тень/кантик
    ctx.shadowColor = '#00FF00';
    ctx.shadowBlur = 8;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = 0;
    ctx.strokeStyle = '#00FF00';
    ctx.lineWidth = 4;
    ctx.stroke();

    // Восстанавливаем состояние
    ctx.restore();
  }


  
  // Рисуем локацию, если есть
  if (tileData?.location && !tileData.fogOfWar) {
    const locationColor = getLocationColor(tileData);
    if (locationColor) {
      drawLocationOverlay(
        ctx,
        centerX,
        centerY,
        halfTileW,
        halfTileH,
        locationColor,
        0.5 // Прозрачность локации
      );
    }
  }
};