import { LocationSubtype } from '../../shared/enums';
import { PresetLocationMap } from './presets/presetType';
import { LocationConfig } from './constants/locationConfig';

// Определение типа LegendMap
type LegendMap = Record<string, string | number>;

// Импорты всех пресетов зданий
import { smallHousePresets } from './presets/buildings/smallHouse';
import { smallHotelPresets } from './presets/buildings/smallHotel';
import { mediumHousePresets } from './presets/buildings/mediumHouse';
import { bigHousePresets } from './presets/buildings/bigHouse';
import { fieldPresets } from './presets/buildings/field';
import { firstAidPresets } from './presets/buildings/firstAid';
import { smallShopPresets } from './presets/buildings/smallShop';
import { barPresets } from './presets/buildings/bar';
import { barakPresets } from './presets/buildings/baraks';
import { gunShopPresets } from './presets/buildings/gunShop';
import { warehousePresets } from './presets/buildings/warehouse';
import { toiletPresets } from './presets/buildings/toilet';
import { parkPresets } from './presets/buildings/park';
import { parkingPresets } from './presets/buildings/parking';

// Интерфейс для конфигурации пресета с шансом и количеством
interface PresetWithChance {
  presets: PresetLocationMap[];
  chance: number; // шанс применения 0-100
  min: number; // минимальное количество на локации (min: 1 = required, игнорирует chance)
  max: number; // максимальное количество на локации
}

// Паттерны размещения зданий
enum PlacementPattern {
  SPIRAL = 'spiral'     // по спирали от центра наружу
}

// Конфигурация размещения зданий для каждого подтипа локации
const BUILDING_PLACEMENT_CONFIG: Partial<Record<LocationSubtype, {
  availablePresets: PresetWithChance[];
  placementPatterns: { pattern: PlacementPattern; chance: number }[];
  minDistance: number; // минимальное расстояние между зданиями
}>> = {
  [LocationSubtype.TOWN]: {
    availablePresets: [
      { presets: smallHousePresets, chance: 80, min: 2, max: 4 },                    // 2-4 маленьких дома
      { presets: mediumHousePresets, chance: 60, min: 1, max: 3 },                   // 1-3 средних дома (обязательно)
      { presets: bigHousePresets, chance: 30, min: 0, max: 2 },                      // 0-2 больших дома
      { presets: smallHotelPresets, chance: 40, min: 0, max: 1 },                    // 0-1 отель
      { presets: firstAidPresets, chance: 70, min: 1, max: 1 },                      // обязательно 1 медпункт
      { presets: smallShopPresets, chance: 80, min: 1, max: 2 },                     // обязательно 1-2 магазина
      { presets: barPresets, chance: 50, min: 0, max: 1 },                           // 0-1 бар
      { presets: gunShopPresets, chance: 40, min: 0, max: 1 },                       // 0-1 оружейный
      { presets: warehousePresets, chance: 30, min: 0, max: 1 },                     // 0-1 склад
      { presets: toiletPresets, chance: 60, min: 0, max: 1 },                        // 0-2 туалета
      { presets: parkPresets, chance: 25, min: 0, max: 1 },                          // 0-1 парк
      { presets: parkingPresets, chance: 20, min: 0, max: 1 }                        // 0-1 парковка
    ],
    placementPatterns: [
      { pattern: PlacementPattern.SPIRAL, chance: 100 }
    ],
    minDistance: 3
  },

  [LocationSubtype.VILLAGE]: {
    availablePresets: [
      { presets: smallHousePresets, chance: 90, min: 1, max: 3 },      // 1-3 маленьких дома
      { presets: mediumHousePresets, chance: 40, min: 0, max: 1 },     // 0-1 средний дом
      { presets: fieldPresets, chance: 60, min: 0, max: 2 },           // 0-2 поля
      { presets: firstAidPresets, chance: 30, min: 0, max: 1 },        // 0-1 медпункт
      { presets: smallShopPresets, chance: 50, min: 0, max: 1 },       // 0-1 магазин
      { presets: barPresets, chance: 40, min: 0, max: 1 },             // 0-1 бар
      { presets: warehousePresets, chance: 50, min: 0, max: 1 },       // 0-1 склад
      { presets: toiletPresets, chance: 70, min: 0, max: 1 }           // 0-1 туалет
    ],
    placementPatterns: [
      { pattern: PlacementPattern.SPIRAL, chance: 100 }
    ],
    minDistance: 2
  },

  [LocationSubtype.CAMP]: {
    availablePresets: [
      { presets: smallHousePresets, chance: 70, min: 1, max: 2 },      // 1-2 маленьких дома
      { presets: smallHotelPresets, chance: 80, min: 0, max: 1 },      // 0-1 отель
      { presets: barakPresets, chance: 60, min: 0, max: 2 },           // 0-2 барака
      { presets: firstAidPresets, chance: 80, min: 1, max: 1 },        // обязательно 1 медпункт
      { presets: smallShopPresets, chance: 40, min: 0, max: 1 },       // 0-1 магазин
      { presets: gunShopPresets, chance: 70, min: 0, max: 1 },         // 0-1 оружейный
      { presets: warehousePresets, chance: 60, min: 0, max: 1 }       // обязательно 1-2 туалета
    ],
    placementPatterns: [
      { pattern: PlacementPattern.SPIRAL, chance: 100 }
    ],
    minDistance: 2
  },

  [LocationSubtype.FARM]: {
    availablePresets: [
      { presets: smallHousePresets, chance: 60, min: 0, max: 1 },      // 0-1 маленький дом
      { presets: fieldPresets, chance: 90, min: 2, max: 4 },           // обязательно 2-4 поля
      { presets: warehousePresets, chance: 80, min: 1, max: 2 },       // обязательно 1-2 склада
      { presets: barPresets, chance: 20, min: 0, max: 1 }         // 0-1 туалет
    ],
    placementPatterns: [
      { pattern: PlacementPattern.SPIRAL, chance: 100 }
    ],
    minDistance: 1
  }
};

// Дефолтная конфигурация для неопределенных подтипов
const DEFAULT_PLACEMENT_CONFIG = {
  availablePresets: [],
  requiredBuildings: [],
  placementPatterns: [{ pattern: PlacementPattern.SPIRAL, chance: 100 }],
  minDistance: 2
};

// Интерфейс для занятой области
interface OccupiedArea {
  x: number;
  y: number;
  width: number;
  height: number;
}

// Интерфейс для позиции здания
interface BuildingPosition {
  x: number;
  y: number;
  preset: PresetLocationMap;
  margins: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
}

/**
 * Генерирует случайные отступы для пресета (2-4 клетки в каждую сторону)
 */
function generateRandomMargins(rng: () => number): { top: number; right: number; bottom: number; left: number } {
  return {
    top: 1 + Math.floor(rng() * 3),     // 2-4
    right: 1 + Math.floor(rng() * 3),   // 2-4 
    bottom:  1 + Math.floor(rng() * 3),  // 2-4
    left: 1 + Math.floor(rng() * 3)     // 2-4
  };
}

/**
 * Функция зеркалирования пресета - применяет изометрическое зеркалирование (меняет X и Y местами)
 */
function shouldMirrorPreset(preset: PresetLocationMap, rng: () => number): boolean {
  return preset.mirror && rng() < 0.5;
}

/**
 * Применяет изометрическое зеркалирование - меняет X и Y координаты местами
 */
function applyIsometricMirror(preset: PresetLocationMap): PresetLocationMap {
  const newTokenMap: (string | number)[][] = [];
  
  // Транспонируем матрицу (меняем строки и столбцы местами)
  for (let x = 0; x < preset.width; x++) {
    newTokenMap[x] = [];
    for (let y = 0; y < preset.height; y++) {
      newTokenMap[x][y] = preset.tokenMap[y][x];
    }
  }
  
  return {
    ...preset,
    width: preset.height,  // меняем размеры местами
    height: preset.width,
    tokenMap: newTokenMap,
    name: `${preset.name}_isometric_mirror`
  };
}

/**
 * Определяет центр локации
 */
function getLocationCenter(width: number, height: number): { x: number; y: number } {
  return {
    x: Math.floor(width / 2),
    y: Math.floor(height / 2)
  };
}

/**
 * Проверяет коллизию между областями с учетом отступов зданий
 */
function checkCollisionWithMargins(
  area1: OccupiedArea, 
  area2: OccupiedArea, 
  margins1: { top: number; right: number; bottom: number; left: number },
  margins2: { top: number; right: number; bottom: number; left: number }
): boolean {
  // Расширяем первую область с учетом ее отступов
  const expandedArea1 = {
    x: area1.x - margins1.left,
    y: area1.y - margins1.top,
    width: area1.width + margins1.left + margins1.right,
    height: area1.height + margins1.top + margins1.bottom
  };

  // Расширяем вторую область с учетом ее отступов
  const expandedArea2 = {
    x: area2.x - margins2.left,
    y: area2.y - margins2.top,
    width: area2.width + margins2.left + margins2.right,
    height: area2.height + margins2.top + margins2.bottom
  };

  return !(
    expandedArea1.x + expandedArea1.width <= expandedArea2.x ||
    expandedArea2.x + expandedArea2.width <= expandedArea1.x ||
    expandedArea1.y + expandedArea1.height <= expandedArea2.y ||
    expandedArea2.y + expandedArea2.height <= expandedArea1.y
  );
}

/**
 * Проверяет, помещается ли здание в границы локации с учетом отступов
 */
function isWithinBoundsWithMargins(
  x: number, 
  y: number, 
  presetWidth: number, 
  presetHeight: number,
  margins: { top: number; right: number; bottom: number; left: number },
  locationWidth: number, 
  locationHeight: number, 
  borderMargin: number = 5
): boolean {
  const totalWidth = margins.left + presetWidth + margins.right;
  const totalHeight = margins.top + presetHeight + margins.bottom;
  const adjustedX = x - margins.left;
  const adjustedY = y - margins.top;
  
  return adjustedX >= borderMargin && 
         adjustedY >= borderMargin && 
         adjustedX + totalWidth <= locationWidth - borderMargin && 
         adjustedY + totalHeight <= locationHeight - borderMargin;
}

/**
 * Генерирует позиции по спирали от центра наружу
 */
function generateSpiralPositions(centerX: number, centerY: number, maxRadius: number): Array<{x: number, y: number}> {
  const positions: Array<{x: number, y: number}> = [];
  
  for (let radius = 0; radius <= maxRadius; radius++) {
    if (radius === 0) {
      positions.push({ x: centerX, y: centerY });
      continue;
    }
    
    // Генерируем позиции по кругу
    const circumference = 8 * radius;
    for (let i = 0; i < circumference; i++) {
      const angle = (2 * Math.PI * i) / circumference;
      const x = Math.round(centerX + radius * Math.cos(angle));
      const y = Math.round(centerY + radius * Math.sin(angle));
      positions.push({ x, y });
    }
  }
  
  return positions;
}

/**
 * Получает пресеты для размещения с учетом min/max количества
 */
function getPresetsToPlace(config: typeof BUILDING_PLACEMENT_CONFIG[LocationSubtype], totalBuildingSlots: number, rng: () => number): PresetLocationMap[] {
  const presetsToPlace: PresetLocationMap[] = [];
  let remainingSlots = totalBuildingSlots;

  // Сначала добавляем обязательные пресеты (где min > 0)
  for (const presetConfig of config.availablePresets) {
    if (presetConfig.min > 0) {
      const countToPlace = presetConfig.min + Math.floor(rng() * (presetConfig.max - presetConfig.min + 1));
      
      for (let i = 0; i < countToPlace && remainingSlots > 0; i++) {
        // Если min: 1, то это обязательное здание - игнорируем chance
        if (presetConfig.min === 1 || rng() * 100 < presetConfig.chance) {
          const preset = presetConfig.presets[Math.floor(rng() * presetConfig.presets.length)];
          presetsToPlace.push(preset);
          remainingSlots--;
        }
      }
    }
  }

  // Затем заполняем оставшиеся слоты необязательными пресетами (где min = 0)
  while (remainingSlots > 0) {
    const availableOptional = config.availablePresets.filter(p => p.min === 0);
    if (availableOptional.length === 0) break;

    let placed = false;
    for (const presetConfig of availableOptional) {
      if (rng() * 100 < presetConfig.chance) {
        // Проверяем, не превышаем ли max для этого типа
        const currentCount = presetsToPlace.filter(p => 
          presetConfig.presets.some(configPreset => configPreset.name === p.name)
        ).length;
        
        if (currentCount < presetConfig.max) {
          const preset = presetConfig.presets[Math.floor(rng() * presetConfig.presets.length)];
          presetsToPlace.push(preset);
          remainingSlots--;
          placed = true;
          break;
        }
      }
    }
    
    if (!placed) break; // если ничего не удалось разместить, выходим
  }

  return presetsToPlace;
}

/**
 * Выбирает паттерн размещения с учетом шанса
 */
function selectPlacementPattern(patterns: { pattern: PlacementPattern; chance: number }[], rng: () => number): PlacementPattern {
  const random = rng() * 100;
  let cumulative = 0;
  
  for (const patternConfig of patterns) {
    cumulative += patternConfig.chance;
    if (random <= cumulative) {
      return patternConfig.pattern;
    }
  }
  
  return patterns[0].pattern; // fallback
}

/**
 * Главная функция размещения пресетов зданий для комплексных локаций
 */
export async function applyBuildingsPresets(
  location: any,
  config: LocationConfig,
  subtype: LocationSubtype,
  rng: () => number,
  legend: LegendMap
): Promise<void> {
  const placementConfig = BUILDING_PLACEMENT_CONFIG[subtype] || DEFAULT_PLACEMENT_CONFIG;
  
  if (!placementConfig || placementConfig.availablePresets.length === 0) {
    console.log(`No building presets configured for subtype: ${subtype}`);
    return;
  }

  const [locationWidth, locationHeight] = location.locationSize;
  const center = getLocationCenter(locationWidth, locationHeight);
  
  // Определяем количество зданий из LocationConfig
  const buildingCount = config.buildings 
    ? config.buildings.min + Math.floor(rng() * (config.buildings.max - config.buildings.min + 1))
    : 3;

  console.log(`Placing ${buildingCount} buildings for ${subtype} at center (${center.x}, ${center.y})`);

  // Выбираем паттерн размещения
  const selectedPattern = selectPlacementPattern(placementConfig.placementPatterns, rng);
  console.log(`Using placement pattern: ${selectedPattern}`);

  // Генерируем возможные позиции в зависимости от паттерна
  let candidatePositions: Array<{x: number, y: number}> = [];
  
  switch (selectedPattern) {
    case PlacementPattern.SPIRAL:
      candidatePositions = generateSpiralPositions(center.x, center.y, Math.min(locationWidth, locationHeight) / 4);
      break;
  }

  // Массив занятых областей для проверки коллизий
  const occupiedAreas: OccupiedArea[] = [];
  const placedBuildings: BuildingPosition[] = [];

  // Получаем пресеты для размещения с учетом min/max
  const buildingsToPlace = getPresetsToPlace(placementConfig, buildingCount, rng);

  console.log(`Planning to place ${buildingsToPlace.length} buildings: ${buildingsToPlace.map(p => p.name).join(', ')}`);

  // Размещаем здания
  for (const preset of buildingsToPlace) {
    // Применяем изометрическое зеркалирование с шансом 50%
    const finalPreset = shouldMirrorPreset(preset, rng) ? applyIsometricMirror(preset) : preset;
    const margins = generateRandomMargins(rng);
    let placed = false;

    for (const pos of candidatePositions) {
      const area: OccupiedArea = {
        x: pos.x,
        y: pos.y,
        width: finalPreset.width,
        height: finalPreset.height
      };

      // Проверяем границы локации с учетом отступов
      if (!isWithinBoundsWithMargins(pos.x, pos.y, finalPreset.width, finalPreset.height, margins, locationWidth, locationHeight)) {
        continue;
      }

      // Проверяем коллизии с уже размещенными зданиями с учетом отступов
      const hasCollision = placedBuildings.some(placedBuilding => {
        const placedArea: OccupiedArea = {
          x: placedBuilding.x,
          y: placedBuilding.y,
          width: placedBuilding.preset.width,
          height: placedBuilding.preset.height
        };
        return checkCollisionWithMargins(area, placedArea, margins, placedBuilding.margins);
      });

      if (!hasCollision) {
        // Размещаем здание
        occupiedAreas.push(area);
        placedBuildings.push({ x: pos.x, y: pos.y, preset: finalPreset, margins });
        placed = true;
        console.log(`Placed building ${finalPreset.name} at (${pos.x}, ${pos.y}), margins: top:${margins.top} right:${margins.right} bottom:${margins.bottom} left:${margins.left}`);
        break;
      }
    }

    if (!placed) {
      console.warn(`Could not place building ${preset.name}`);
    }
  }

  // Применяем пресеты к локации
  await applyPresetsToLocation(location, placedBuildings, legend);
  
  console.log(`Successfully placed ${placedBuildings.length} buildings for ${subtype}`);
}

/**
 * Применяет размещенные пресеты к локации
 */
async function applyPresetsToLocation(
  location: any,
  buildings: BuildingPosition[],
  legend: LegendMap
): Promise<void> {
  if (!location.decorations) location.decorations = {};

  const tokenToDecoration: Record<number, string> = {};
  for (const [decorationName, token] of Object.entries(legend)) {
    tokenToDecoration[token as number] = decorationName;
  }

  for (const building of buildings) {
    const { x: offsetX, y: offsetY, preset, margins } = building;

    // Очищаем область перед размещением с учетом отступов
    await clearBuildingAreaWithMargins(location, offsetX, offsetY, preset.width, preset.height, margins);

    // Применяем токены пресета (зеркалирование уже применено к самому пресету)
    for (let y = 0; y < preset.height; y++) {
      for (let x = 0; x < preset.width; x++) {
        const token = preset.tokenMap[y] && preset.tokenMap[y][x];
        
        if (!token || token === 999) continue;

        const tokenNum = typeof token === 'number' ? token : parseInt(token.toString());
        if (isNaN(tokenNum)) continue;

        const targetX = offsetX + x;
        const targetY = offsetY + y;

        const decorationName = tokenToDecoration[tokenNum];
        if (decorationName && decorationName !== 'none') {
          if (!location.decorations[decorationName]) location.decorations[decorationName] = [];
          location.decorations[decorationName].push([targetX, targetY]);
        }
      }
    }
  }
}

/**
 * Очищает область перед размещением здания с учетом отступов
 */
async function clearBuildingAreaWithMargins(
  location: any,
  startX: number,
  startY: number,
  width: number,
  height: number,
  margins: { top: number; right: number; bottom: number; left: number }
): Promise<void> {
  if (!location.decorations) return;

  // Вычисляем расширенную область с отступами
  const expandedStartX = startX - margins.left;
  const expandedStartY = startY - margins.top;
  const expandedWidth = width + margins.left + margins.right;
  const expandedHeight = height + margins.top + margins.bottom;

  // Очищаем декорации в расширенной области
  for (const decorationName in location.decorations) {
    const decorations = location.decorations[decorationName];
    if (!Array.isArray(decorations)) continue;

    for (let i = decorations.length - 1; i >= 0; i--) {
      const [x, y] = decorations[i];
      if (x >= expandedStartX && x < expandedStartX + expandedWidth && 
          y >= expandedStartY && y < expandedStartY + expandedHeight) {
        decorations.splice(i, 1);
      }
    }

    // Удаляем пустые массивы
    if (decorations.length === 0) {
      delete location.decorations[decorationName];
    }
  }
}
