import { LocationConfig } from './constants/locationConfig';
import { LocationDecorations, LocationSubtype, TerrainType } from '../../shared/enums';
import { Point } from '../../shared/types/Location';

// Типы паттернов улиц
enum StreetPattern {
  T = 'T',
  H = 'H',
  U = 'U'  // П-образный паттерн
}

// Направления улиц
enum StreetDirection {
  NORTH_SOUTH = 'north_south',
  EAST_WEST = 'east_west'
}

// Интерфейс для области улицы
interface StreetArea {
  x: number;
  y: number;
  width: number;
  height: number;
}

// Константы для улиц
const STREET_WIDTH = 11; // Общая ширина улицы
const SIDEWALK_WIDTH = 2; // Ширина тротуара с каждой стороны (клетки 0-1 и 9-10)
const ROAD_START = 2; // Начало проезжей части
const ROAD_END = 8; // Конец проезжей части
const CAR_SPAWN_INTERVAL = 20; // Каждые 20 тайлов
const MAX_CARS_PER_CITY = 4; // Максимум машин на город
const LIGHT_INTERVAL = 10; // Каждые 10 клеток фонари
const DECORATION_PRESERVE_CHANCE = 0.05; // 5% шанс сохранить декорации в области дорог

/**
 * Главная функция генерации улиц для локации
 */
export async function generateStreetsForLocation(
  location: any,
  config: LocationConfig,
  rng: () => number
): Promise<StreetArea[]> {
  // Проверяем, нужны ли улицы для данного типа локации
  if (!shouldGenerateStreets(location.subtype)) {
    return [];
  }

  const [width, height] = location.locationSize;
  const centerX = Math.floor(width / 2);
  const centerY = Math.floor(height / 2);

  // Выбираем паттерн и направление
  const pattern = selectStreetPattern(rng);
  const direction = selectStreetDirection(rng);

  console.log(`Generating streets: pattern=${pattern}, direction=${direction}, center=(${centerX}, ${centerY})`);

  // Генерируем улицы
  const streetAreas = generateStreetAreas(pattern, direction, centerX, centerY, width, height, rng);

  // Рисуем улицы
  await drawStreets(location, streetAreas, rng);

  // Размещаем объекты на улицах
  await placeStreetObjects(location, streetAreas, rng);

  return streetAreas;
}

/**
 * Проверяет, нужно ли генерировать улицы для данного подтипа локации
 */
function shouldGenerateStreets(subtype: LocationSubtype): boolean {
  return subtype === LocationSubtype.TOWN || subtype === LocationSubtype.VILLAGE;
}

/**
 * Выбирает случайный паттерн улиц
 */
function selectStreetPattern(rng: () => number): StreetPattern {
  const patterns = Object.values(StreetPattern);
  return patterns[Math.floor(rng() * patterns.length)];
}

/**
 * Выбирает направление улиц (только одно направление)
 */
function selectStreetDirection(rng: () => number): StreetDirection {
  return rng() < 0.5 ? StreetDirection.NORTH_SOUTH : StreetDirection.EAST_WEST;
}

/**
 * Генерирует области улиц в зависимости от паттерна
 */
function generateStreetAreas(
  pattern: StreetPattern,
  direction: StreetDirection,
  centerX: number,
  centerY: number,
  width: number,
  height: number,
  rng: () => number
): StreetArea[] {
  const areas: StreetArea[] = [];

  switch (pattern) {
    case StreetPattern.T:
      areas.push(...generateTPattern(direction, centerX, centerY, width, height));
      break;
    case StreetPattern.H:
      areas.push(...generateHPattern(direction, centerX, centerY, width, height));
      break;
    case StreetPattern.U:
      areas.push(...generateUPattern(direction, centerX, centerY, width, height));
      break;
  }

  return areas.filter(area => isValidStreetArea(area, width, height));
}

/**
 * Генерирует T-образный паттерн
 */
function generateTPattern(
  direction: StreetDirection,
  centerX: number,
  centerY: number,
  width: number,
  height: number
): StreetArea[] {
  const areas: StreetArea[] = [];

  if (direction === StreetDirection.NORTH_SOUTH) {
    // Вертикальная линия через центр
    areas.push({
      x: centerX - Math.floor(STREET_WIDTH / 2),
      y: 0,
      width: STREET_WIDTH,
      height: height
    });
    // Горизонтальная линия в верхней части
    areas.push({
      x: 0,
      y: Math.floor(height / 3) - Math.floor(STREET_WIDTH / 2),
      width: width,
      height: STREET_WIDTH
    });
  } else {
    // Горизонтальная линия через центр
    areas.push({
      x: 0,
      y: centerY - Math.floor(STREET_WIDTH / 2),
      width: width,
      height: STREET_WIDTH
    });
    // Вертикальная линия в левой части
    areas.push({
      x: Math.floor(width / 3) - Math.floor(STREET_WIDTH / 2),
      y: 0,
      width: STREET_WIDTH,
      height: height
    });
  }

  return areas;
}

/**
 * Генерирует H-образный паттерн
 */
function generateHPattern(
  direction: StreetDirection,
  centerX: number,
  centerY: number,
  width: number,
  height: number
): StreetArea[] {
  const areas: StreetArea[] = [];

  if (direction === StreetDirection.NORTH_SOUTH) {
    // Две вертикальные линии
    areas.push({
      x: Math.floor(width / 3) - Math.floor(STREET_WIDTH / 2),
      y: 0,
      width: STREET_WIDTH,
      height: height
    });
    areas.push({
      x: Math.floor(2 * width / 3) - Math.floor(STREET_WIDTH / 2),
      y: 0,
      width: STREET_WIDTH,
      height: height
    });
    // Горизонтальная соединяющая линия
    areas.push({
      x: 0,
      y: centerY - Math.floor(STREET_WIDTH / 2),
      width: width,
      height: STREET_WIDTH
    });
  } else {
    // Две горизонтальные линии
    areas.push({
      x: 0,
      y: Math.floor(height / 3) - Math.floor(STREET_WIDTH / 2),
      width: width,
      height: STREET_WIDTH
    });
    areas.push({
      x: 0,
      y: Math.floor(2 * height / 3) - Math.floor(STREET_WIDTH / 2),
      width: width,
      height: STREET_WIDTH
    });
    // Вертикальная соединяющая линия
    areas.push({
      x: centerX - Math.floor(STREET_WIDTH / 2),
      y: 0,
      width: STREET_WIDTH,
      height: height
    });
  }

  return areas;
}

/**
 * Генерирует П-образный паттерн
 */
function generateUPattern(
  direction: StreetDirection,
  centerX: number,
  centerY: number,
  width: number,
  height: number
): StreetArea[] {
  const areas: StreetArea[] = [];

  if (direction === StreetDirection.NORTH_SOUTH) {
    // Две вертикальные линии по краям
    areas.push({
      x: Math.floor(width / 4) - Math.floor(STREET_WIDTH / 2),
      y: 0,
      width: STREET_WIDTH,
      height: height
    });
    areas.push({
      x: Math.floor(3 * width / 4) - Math.floor(STREET_WIDTH / 2),
      y: 0,
      width: STREET_WIDTH,
      height: height
    });
    // Горизонтальная соединяющая линия внизу
    areas.push({
      x: Math.floor(width / 4),
      y: Math.floor(3 * height / 4) - Math.floor(STREET_WIDTH / 2),
      width: Math.floor(width / 2),
      height: STREET_WIDTH
    });
  } else {
    // Две горизонтальные линии по краям
    areas.push({
      x: 0,
      y: Math.floor(height / 4) - Math.floor(STREET_WIDTH / 2),
      width: width,
      height: STREET_WIDTH
    });
    areas.push({
      x: 0,
      y: Math.floor(3 * height / 4) - Math.floor(STREET_WIDTH / 2),
      width: width,
      height: STREET_WIDTH
    });
    // Вертикальная соединяющая линия справа
    areas.push({
      x: Math.floor(3 * width / 4) - Math.floor(STREET_WIDTH / 2),
      y: Math.floor(height / 4),
      width: STREET_WIDTH,
      height: Math.floor(height / 2)
    });
  }

  return areas;
}

/**
 * Проверяет валидность области улицы
 */
function isValidStreetArea(area: StreetArea, mapWidth: number, mapHeight: number): boolean {
  return area.x >= 0 && area.y >= 0 &&
         area.x + area.width <= mapWidth &&
         area.y + area.height <= mapHeight &&
         area.width > 0 && area.height > 0;
}

/**
 * Рисует улицы на локации
 */
async function drawStreets(location: any, streetAreas: StreetArea[], rng: () => number): Promise<void> {
  // Инициализируем floor если его нет
  if (!location.floor) {
    location.floor = {};
  }
  if (!location.floor[TerrainType.TILES]) {
    location.floor[TerrainType.TILES] = [];
  }
  if (!location.floor[TerrainType.ASPHALT]) {
    location.floor[TerrainType.ASPHALT] = [];
  }

  for (const area of streetAreas) {
    await drawSingleStreet(location, area, rng);
  }
}

/**
 * Рисует одну улицу
 */
async function drawSingleStreet(location: any, area: StreetArea, rng: () => number): Promise<void> {
  for (let x = area.x; x < area.x + area.width; x++) {
    for (let y = area.y; y < area.y + area.height; y++) {
      const relativeX = x - area.x;
      const relativeY = y - area.y;

      // Определяем тип покрытия в зависимости от позиции в улице
      const terrainType = getStreetTerrainType(relativeX, relativeY, area);

      // Добавляем тайл в соответствующий массив
      if (terrainType === TerrainType.TILES) {
        location.floor[TerrainType.TILES].push([x, y]);
      } else if (terrainType === TerrainType.ASPHALT) {
        location.floor[TerrainType.ASPHALT].push([x, y]);
      }

      // Очищаем существующие декорации с шансом сохранения 5%
      await clearExistingDecorations(location, x, y, rng);

      // Добавляем decoration.road с 20% шансом на асфальте
      if (terrainType === TerrainType.ASPHALT && rng() < 0.2) {
        if (!location.decorations[LocationDecorations.ROAD]) {
          location.decorations[LocationDecorations.ROAD] = [];
        }
        location.decorations[LocationDecorations.ROAD].push([x, y]);
      }
    }
  }
}

/**
 * Определяет тип покрытия для конкретной позиции в улице
 */
function getStreetTerrainType(relativeX: number, relativeY: number, area: StreetArea): TerrainType {
  // Для горизонтальных улиц (ширина больше высоты)
  if (area.width > area.height) {
    // Тротуары: клетки 0-1 и 9-10 по Y
    if (relativeY <= 1 || relativeY >= STREET_WIDTH - 2) {
      return TerrainType.TILES;
    }
    // Асфальт: клетки 2-8 по Y
    if (relativeY >= ROAD_START && relativeY <= ROAD_END) {
      return TerrainType.ASPHALT;
    }
  } else {
    // Для вертикальных улиц (высота больше ширины)
    // Тротуары: клетки 0-1 и 9-10 по X
    if (relativeX <= 1 || relativeX >= STREET_WIDTH - 2) {
      return TerrainType.TILES;
    }
    // Асфальт: клетки 2-8 по X
    if (relativeX >= ROAD_START && relativeX <= ROAD_END) {
      return TerrainType.ASPHALT;
    }
  }

  // По умолчанию тротуар
  return TerrainType.TILES;
}

/**
 * Очищает существующие декорации с шансом сохранения 5%
 */
async function clearExistingDecorations(location: any, x: number, y: number, rng: () => number): Promise<void> {
  if (!location.decorations) return;

  // Список декораций, которые могут быть очищены в области дорог
  const clearableDecorations = [
    LocationDecorations.GRASS,
    LocationDecorations.BUSH,
    LocationDecorations.TREE,
    LocationDecorations.LOG,
    LocationDecorations.ROCKS,
    LocationDecorations.TIRE,
    LocationDecorations.BARREL,
    LocationDecorations.MUD
  ];

  for (const decorationType of clearableDecorations) {
    const decorations = location.decorations[decorationType];
    if (!Array.isArray(decorations)) continue;

    // Ищем декорацию в данной позиции
    const index = decorations.findIndex(([dx, dy]: Point) => dx === x && dy === y);
    if (index !== -1) {
      // Удаляем с шансом 95% (сохраняем 5%)
      if (rng() > DECORATION_PRESERVE_CHANCE) {
        decorations.splice(index, 1);
      }
    }
  }
}

/**
 * Размещает объекты на улицах
 */
async function placeStreetObjects(location: any, streetAreas: StreetArea[], rng: () => number): Promise<void> {
  let carsPlaced = 0;

  for (const area of streetAreas) {
    // Размещаем машины
    carsPlaced += await placeCars(location, area, carsPlaced, rng);

    // Размещаем фонари на тротуарах
    await placeLights(location, area, rng);

    // Размещаем скамейки и урны
    await placeBenchesAndTrashBins(location, area, rng);

    // Размещаем люки на дороге
    await placeManholeCovers(location, area, rng);

    // Размещаем знаки
    await placeSigns(location, area, rng);

    // Размещаем мусор
    await placeLitter(location, area, rng);
  }
}

/**
 * Размещает машины на дороге
 */
async function placeCars(location: any, area: StreetArea, carsAlreadyPlaced: number, rng: () => number): Promise<number> {
  if (carsAlreadyPlaced >= MAX_CARS_PER_CITY) {
    return 0;
  }

  if (!location.decorations[LocationDecorations.CAR]) {
    location.decorations[LocationDecorations.CAR] = [];
  }

  let carsPlaced = 0;
  const carWidth = 2;
  const carHeight = 5;

  // Определяем направление улицы
  const isHorizontal = area.width > area.height;

  if (isHorizontal) {
    // Горизонтальная улица - машины вдоль полос 3,4 и 7,8
    const lanes = [3, 4, 7, 8];

    for (let x = area.x; x < area.x + area.width; x += CAR_SPAWN_INTERVAL) {
      if (carsAlreadyPlaced + carsPlaced >= MAX_CARS_PER_CITY) break;

      if (rng() < 0.3) { // 30% шанс разместить машину
        const lane = lanes[Math.floor(rng() * lanes.length)];
        const carY = area.y + lane;

        // Иногда размещаем поперек (постапокалипсис)
        if (rng() < 0.2) {
          // Поперек дороги
          if (canPlaceCar(location, x, carY - 2, carHeight, carWidth)) {
            location.decorations[LocationDecorations.CAR].push([x, carY - 2]);
            carsPlaced++;
          }
        } else {
          // Вдоль дороги
          if (canPlaceCar(location, x, carY, carWidth, carHeight)) {
            location.decorations[LocationDecorations.CAR].push([x, carY]);
            carsPlaced++;
          }
        }
      }
    }
  } else {
    // Вертикальная улица - машины вдоль полос 3,4 и 7,8
    const lanes = [3, 4, 7, 8];

    for (let y = area.y; y < area.y + area.height; y += CAR_SPAWN_INTERVAL) {
      if (carsAlreadyPlaced + carsPlaced >= MAX_CARS_PER_CITY) break;

      if (rng() < 0.3) { // 30% шанс разместить машину
        const lane = lanes[Math.floor(rng() * lanes.length)];
        const carX = area.x + lane;

        // Иногда размещаем поперек (постапокалипсис)
        if (rng() < 0.2) {
          // Поперек дороги
          if (canPlaceCar(location, carX - 2, y, carHeight, carWidth)) {
            location.decorations[LocationDecorations.CAR].push([carX - 2, y]);
            carsPlaced++;
          }
        } else {
          // Вдоль дороги
          if (canPlaceCar(location, carX, y, carWidth, carHeight)) {
            location.decorations[LocationDecorations.CAR].push([carX, y]);
            carsPlaced++;
          }
        }
      }
    }
  }

  return carsPlaced;
}

/**
 * Проверяет, можно ли разместить машину в данной позиции
 */
function canPlaceCar(location: any, x: number, y: number, width: number, height: number): boolean {
  const [mapWidth, mapHeight] = location.locationSize;

  // Проверяем границы карты
  if (x < 0 || y < 0 || x + width > mapWidth || y + height > mapHeight) {
    return false;
  }

  // Проверяем, нет ли уже машины в этой области
  const cars = location.decorations[LocationDecorations.CAR] || [];
  for (const [carX, carY] of cars) {
    if (x < carX + 2 && x + width > carX && y < carY + 5 && y + height > carY) {
      return false; // Пересечение с существующей машиной
    }
  }

  return true;
}

/**
 * Размещает фонари на тротуарах (только северная и западная стороны)
 */
async function placeLights(location: any, area: StreetArea, rng: () => number): Promise<void> {
  if (!location.decorations[LocationDecorations.EXTERIORLIGHT]) {
    location.decorations[LocationDecorations.EXTERIORLIGHT] = [];
  }

  const [mapWidth, mapHeight] = location.locationSize;
  const isHorizontal = area.width > area.height;

  if (isHorizontal) {
    // Горизонтальная улица - фонари только на северном тротуаре
    for (let x = area.x; x < area.x + area.width; x += LIGHT_INTERVAL) {
      const lightChance = getEdgeSpawnChance(x, area.y + 1, mapWidth, mapHeight);
      if (rng() < lightChance * 0.8) {
        location.decorations[LocationDecorations.EXTERIORLIGHT].push([x, area.y + 1]);
      }
    }
  } else {
    // Вертикальная улица - фонари только на западном тротуаре
    for (let y = area.y; y < area.y + area.height; y += LIGHT_INTERVAL) {
      const lightChance = getEdgeSpawnChance(area.x + 1, y, mapWidth, mapHeight);
      if (rng() < lightChance * 0.8) {
        location.decorations[LocationDecorations.EXTERIORLIGHT].push([area.x + 1, y]);
      }
    }
  }
}

/**
 * Размещает скамейки и урны (только с севера и запада от асфальта для изометрии)
 */
async function placeBenchesAndTrashBins(location: any, area: StreetArea, rng: () => number): Promise<void> {
  if (!location.decorations[LocationDecorations.BENCH]) {
    location.decorations[LocationDecorations.BENCH] = [];
  }
  if (!location.decorations[LocationDecorations.TRASHBIN]) {
    location.decorations[LocationDecorations.TRASHBIN] = [];
  }

  const isHorizontal = area.width > area.height;

  if (isHorizontal) {
    // Горизонтальная улица - скамейки только на северном тротуаре (изометрия)
    for (let x = area.x; x < area.x + area.width; x += 15) {
      if (rng() < 0.3) {
        // Скамейка 1х2 на северном тротуаре
        const benchY = area.y + 1;
        if (canPlaceBench(location, x, benchY)) {
          location.decorations[LocationDecorations.BENCH].push([x, benchY]);

          // Урна рядом со скамейкой
          if (rng() < 0.7) {
            location.decorations[LocationDecorations.TRASHBIN].push([x + 2, benchY]);
          }
        }
      }
    }
  } else {
    // Вертикальная улица - скамейки только на западном тротуаре (изометрия)
    for (let y = area.y; y < area.y + area.height; y += 15) {
      if (rng() < 0.3) {
        // Скамейка 1х2 на западном тротуаре
        const benchX = area.x + 1;
        if (canPlaceBench(location, benchX, y)) {
          location.decorations[LocationDecorations.BENCH].push([benchX, y]);

          // Урна рядом со скамейкой
          if (rng() < 0.7) {
            location.decorations[LocationDecorations.TRASHBIN].push([benchX, y + 2]);
          }
        }
      }
    }
  }
}

/**
 * Проверяет, можно ли разместить скамейку (многоблок 1х2)
 */
function canPlaceBench(location: any, x: number, y: number): boolean {
  const [mapWidth, mapHeight] = location.locationSize;

  // Проверяем границы карты для скамейки 1х2
  if (x < 0 || y < 0 || x + 1 > mapWidth || y + 2 > mapHeight) {
    return false;
  }

  return true;
}

/**
 * Размещает люки на дороге
 */
async function placeManholeCovers(location: any, area: StreetArea, rng: () => number): Promise<void> {
  if (!location.decorations[LocationDecorations.MANHOLECOVER]) {
    location.decorations[LocationDecorations.MANHOLECOVER] = [];
  }

  const isHorizontal = area.width > area.height;

  if (isHorizontal) {
    // Горизонтальная улица - люки на асфальте (Y = 2-8)
    for (let x = area.x; x < area.x + area.width; x += 25) {
      if (rng() < 0.15) { // 15% шанс
        const manholeY = area.y + ROAD_START + Math.floor(rng() * (ROAD_END - ROAD_START + 1));
        location.decorations[LocationDecorations.MANHOLECOVER].push([x, manholeY]);
      }
    }
  } else {
    // Вертикальная улица - люки на асфальте (X = 2-8)
    for (let y = area.y; y < area.y + area.height; y += 25) {
      if (rng() < 0.15) { // 15% шанс
        const manholeX = area.x + ROAD_START + Math.floor(rng() * (ROAD_END - ROAD_START + 1));
        location.decorations[LocationDecorations.MANHOLECOVER].push([manholeX, y]);
      }
    }
  }
}

/**
 * Размещает знаки
 */
async function placeSigns(location: any, area: StreetArea, rng: () => number): Promise<void> {
  if (!location.decorations[LocationDecorations.SIGN]) {
    location.decorations[LocationDecorations.SIGN] = [];
  }

  const isHorizontal = area.width > area.height;

  if (isHorizontal) {
    // Горизонтальная улица - знаки на тротуарах
    for (let x = area.x; x < area.x + area.width; x += 30) {
      if (rng() < 0.1) { // 10% шанс
        // Случайный тротуар
        const signY = rng() < 0.5 ? area.y + 1 : area.y + area.height - 2;
        location.decorations[LocationDecorations.SIGN].push([x, signY]);
      }
    }
  } else {
    // Вертикальная улица - знаки на тротуарах
    for (let y = area.y; y < area.y + area.height; y += 30) {
      if (rng() < 0.1) { // 10% шанс
        // Случайный тротуар
        const signX = rng() < 0.5 ? area.x + 1 : area.x + area.width - 2;
        location.decorations[LocationDecorations.SIGN].push([signX, y]);
      }
    }
  }
}

/**
 * Размещает мусор везде понемногу
 */
async function placeLitter(location: any, area: StreetArea, rng: () => number): Promise<void> {
  if (!location.decorations[LocationDecorations.LITTER]) {
    location.decorations[LocationDecorations.LITTER] = [];
  }

  // Размещаем мусор по всей области улицы
  for (let x = area.x; x < area.x + area.width; x++) {
    for (let y = area.y; y < area.y + area.height; y++) {
      if (rng() < 0.02) { // 2% шанс на каждую клетку
        location.decorations[LocationDecorations.LITTER].push([x, y]);
      }
    }
  }
}
